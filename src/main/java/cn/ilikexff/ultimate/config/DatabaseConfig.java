package cn.ilikexff.ultimate.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库配置类
 */
@Configuration
@EnableJpaRepositories(basePackages = "cn.ilikexff.ultimate.repository")
@EnableJpaAuditing
@EnableTransactionManagement
public class DatabaseConfig {
    
}
