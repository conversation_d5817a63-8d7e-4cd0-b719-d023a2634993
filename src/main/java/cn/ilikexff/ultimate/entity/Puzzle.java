package cn.ilikexff.ultimate.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 谜题实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "puzzles", indexes = {
        @Index(name = "idx_difficulty", columnList = "difficulty"),
        @Index(name = "idx_puzzle_type", columnList = "puzzle_type"),
        @Index(name = "idx_status", columnList = "status"),
        @Index(name = "idx_is_daily", columnList = "is_daily")
})
public class Puzzle extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "puzzle_id")
    private Long puzzleId;

    @NotBlank(message = "谜题名称不能为空")
    @Column(name = "puzzle_name", nullable = false, length = 200)
    private String puzzleName;

    @NotBlank(message = "谜题描述不能为空")
    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(name = "background_story", columnDefinition = "TEXT")
    private String backgroundStory;

    @Column(name = "clues", columnDefinition = "TEXT")
    private String clues;

    @NotNull(message = "难度等级不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "difficulty", nullable = false)
    private Difficulty difficulty;

    @Min(value = 0, message = "所需U币不能为负数")
    @Column(name = "required_u_coins", nullable = false)
    private Integer requiredUCoins = 0;

    @Min(value = 0, message = "U币奖励不能为负数")
    @Column(name = "u_coins_reward", nullable = false)
    private Integer uCoinsReward = 0;

    @Min(value = 0, message = "推理力奖励不能为负数")
    @Column(name = "reasoning_power_reward", nullable = false)
    private Integer reasoningPowerReward = 0;

    @NotBlank(message = "谜题答案不能为空")
    @Column(name = "answer", nullable = false, length = 500)
    private String answer;

    @Enumerated(EnumType.STRING)
    @Column(name = "puzzle_type", nullable = false)
    private PuzzleType puzzleType = PuzzleType.TEXT;

    @Column(name = "created_by", nullable = false)
    private Long createdBy;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PuzzleStatus status = PuzzleStatus.DRAFT;

    @Column(name = "play_count", nullable = false)
    private Integer playCount = 0;

    @Column(name = "completion_count", nullable = false)
    private Integer completionCount = 0;

    @Column(name = "average_rating")
    private Double averageRating;

    @Column(name = "is_daily", nullable = false)
    private Boolean isDaily = false;

    @Column(name = "daily_date")
    private java.time.LocalDate dailyDate;

    /**
     * 难度等级枚举
     */
    public enum Difficulty {
        EASY,       // 简单
        MEDIUM,     // 中等
        HARD,       // 困难
        EXPERT      // 专家
    }

    /**
     * 谜题类型枚举
     */
    public enum PuzzleType {
        TEXT,       // 文字
        IMAGE,      // 图片
        AUDIO,      // 音频
        VIDEO,      // 视频
        MIXED       // 混合
    }

    /**
     * 谜题状态枚举
     */
    public enum PuzzleStatus {
        DRAFT,      // 草稿
        PUBLISHED,  // 已发布
        ARCHIVED    // 已归档
    }
}
