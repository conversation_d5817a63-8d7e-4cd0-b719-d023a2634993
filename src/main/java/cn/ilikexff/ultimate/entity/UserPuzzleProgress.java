package cn.ilikexff.ultimate.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户谜题进度实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_puzzle_progress", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "puzzle_id"}),
       indexes = {
           @Index(name = "idx_user_id", columnList = "user_id"),
           @Index(name = "idx_puzzle_id", columnList = "puzzle_id"),
           @Index(name = "idx_status", columnList = "status")
       })
public class UserPuzzleProgress extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "progress_id")
    private Long progressId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "puzzle_id", nullable = false)
    private Long puzzleId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ProgressStatus status = ProgressStatus.NOT_STARTED;

    @Column(name = "attempts_count", nullable = false)
    private Integer attemptsCount = 0;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "earned_u_coins", nullable = false)
    private Integer earnedUCoins = 0;

    @Column(name = "earned_reasoning_power", nullable = false)
    private Integer earnedReasoningPower = 0;

    @Column(name = "start_time")
    private LocalDateTime startTime;

    @Column(name = "total_time_spent")
    private Long totalTimeSpent; // 总耗时（秒）

    @Column(name = "hints_used", nullable = false)
    private Integer hintsUsed = 0;

    @Column(name = "rating")
    private Integer rating; // 用户对谜题的评分 1-5

    /**
     * 进度状态枚举
     */
    public enum ProgressStatus {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 进行中
        COMPLETED       // 已完成
    }

    // 关联实体
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puzzle_id", insertable = false, updatable = false)
    private Puzzle puzzle;
}
